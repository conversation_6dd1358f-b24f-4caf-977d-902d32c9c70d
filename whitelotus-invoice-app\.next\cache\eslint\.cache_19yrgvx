[{"E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\invoice\\route.ts": "1", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\invoice\\[id]\\route.ts": "2", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\login\\route.ts": "3", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\logout\\route.ts": "4", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\me\\route.ts": "5", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\csrf\\route.ts": "6", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\invoice\\[filename]\\route.ts": "7", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\dashboard\\DashboardContent.tsx": "8", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\dashboard\\page.tsx": "9", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\invoice\\new\\page.tsx": "10", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\invoice\\[id]\\InvoiceDetailsContent.tsx": "11", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\invoice\\[id]\\page.tsx": "12", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\layout.tsx": "13", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\login\\page.tsx": "14", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\page.tsx": "15", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\components\\ErrorBoundary.tsx": "16", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\components\\InvoiceCard.tsx": "17", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\components\\LoadingSpinner.tsx": "18", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\components\\Pagination.tsx": "19", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\context\\AuthContext.tsx": "20", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\hooks\\useInvoices.ts": "21", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\api.ts": "22", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\auth.ts": "23", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\config.ts": "24", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\mockData.ts": "25", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\security.ts": "26", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\services\\authService.ts": "27", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\services\\invoiceService.ts": "28", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\middleware.ts": "29", "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\types\\index.ts": "30"}, {"size": 4957, "mtime": 1758110004163, "results": "31", "hashOfConfig": "32"}, {"size": 2148, "mtime": 1758109932235, "results": "33", "hashOfConfig": "32"}, {"size": 2587, "mtime": 1758110016293, "results": "34", "hashOfConfig": "32"}, {"size": 687, "mtime": 1758108993335, "results": "35", "hashOfConfig": "32"}, {"size": 825, "mtime": 1758109000800, "results": "36", "hashOfConfig": "32"}, {"size": 945, "mtime": 1758109466841, "results": "37", "hashOfConfig": "32"}, {"size": 2242, "mtime": 1758109977526, "results": "38", "hashOfConfig": "32"}, {"size": 7564, "mtime": 1758109189587, "results": "39", "hashOfConfig": "32"}, {"size": 1668, "mtime": 1758109162399, "results": "40", "hashOfConfig": "32"}, {"size": 12064, "mtime": 1758109290195, "results": "41", "hashOfConfig": "32"}, {"size": 8610, "mtime": 1758109241869, "results": "42", "hashOfConfig": "32"}, {"size": 1647, "mtime": 1758109210902, "results": "43", "hashOfConfig": "32"}, {"size": 955, "mtime": 1758109358898, "results": "44", "hashOfConfig": "32"}, {"size": 5458, "mtime": 1758109019629, "results": "45", "hashOfConfig": "32"}, {"size": 431, "mtime": 1758109791193, "results": "46", "hashOfConfig": "32"}, {"size": 2907, "mtime": 1758109306135, "results": "47", "hashOfConfig": "32"}, {"size": 3754, "mtime": 1758109149493, "results": "48", "hashOfConfig": "32"}, {"size": 381, "mtime": 1758109114835, "results": "49", "hashOfConfig": "32"}, {"size": 4137, "mtime": 1758109131607, "results": "50", "hashOfConfig": "32"}, {"size": 2231, "mtime": 1758108949049, "results": "51", "hashOfConfig": "32"}, {"size": 1576, "mtime": 1758109479158, "results": "52", "hashOfConfig": "32"}, {"size": 3080, "mtime": 1758108875546, "results": "53", "hashOfConfig": "32"}, {"size": 2130, "mtime": 1758108888894, "results": "54", "hashOfConfig": "32"}, {"size": 890, "mtime": 1758108861266, "results": "55", "hashOfConfig": "32"}, {"size": 4027, "mtime": 1758109051840, "results": "56", "hashOfConfig": "32"}, {"size": 3755, "mtime": 1758109444359, "results": "57", "hashOfConfig": "32"}, {"size": 2188, "mtime": 1758108937795, "results": "58", "hashOfConfig": "32"}, {"size": 3249, "mtime": 1758108924001, "results": "59", "hashOfConfig": "32"}, {"size": 2127, "mtime": 1758108969791, "results": "60", "hashOfConfig": "32"}, {"size": 2371, "mtime": 1758108850125, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ko7385", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\invoice\\route.ts", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\invoice\\[id]\\route.ts", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\login\\route.ts", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\logout\\route.ts", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\auth\\me\\route.ts", ["152"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\csrf\\route.ts", ["153"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\api\\invoice\\[filename]\\route.ts", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\dashboard\\DashboardContent.tsx", ["154", "155"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\dashboard\\page.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\invoice\\new\\page.tsx", ["156"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\invoice\\[id]\\InvoiceDetailsContent.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\invoice\\[id]\\page.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\layout.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\login\\page.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\app\\page.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\components\\ErrorBoundary.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\components\\InvoiceCard.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\components\\LoadingSpinner.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\components\\Pagination.tsx", ["157"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\context\\AuthContext.tsx", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\hooks\\useInvoices.ts", ["158", "159"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\api.ts", ["160", "161", "162"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\auth.ts", ["163", "164", "165", "166", "167"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\config.ts", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\mockData.ts", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\security.ts", ["168", "169", "170", "171"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\services\\authService.ts", ["172", "173"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\lib\\services\\invoiceService.ts", ["174"], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\middleware.ts", [], [], "E:\\Workspace\\Whitelotus assignment\\whitelotus_assignment_web\\whitelotus-invoice-app\\src\\types\\index.ts", ["175", "176", "177", "178", "179"], [], {"ruleId": "180", "severity": 1, "message": "181", "line": 5, "column": 27, "nodeType": null, "messageId": "182", "endLine": 5, "endColumn": 34}, {"ruleId": "180", "severity": 1, "message": "181", "line": 5, "column": 27, "nodeType": null, "messageId": "182", "endLine": 5, "endColumn": 34}, {"ruleId": "183", "severity": 1, "message": "184", "line": 32, "column": 6, "nodeType": "185", "endLine": 32, "endColumn": 19, "suggestions": "186"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 48, "column": 36, "nodeType": "189", "messageId": "190", "endLine": 48, "endColumn": 39, "suggestions": "191"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 223, "column": 82, "nodeType": "189", "messageId": "190", "endLine": 223, "endColumn": 85, "suggestions": "192"}, {"ruleId": "193", "severity": 2, "message": "194", "line": 19, "column": 9, "nodeType": "195", "messageId": "196", "endLine": 19, "endColumn": 16, "fix": "197"}, {"ruleId": "180", "severity": 1, "message": "198", "line": 3, "column": 10, "nodeType": null, "messageId": "182", "endLine": 3, "endColumn": 17}, {"ruleId": "187", "severity": 2, "message": "188", "line": 31, "column": 26, "nodeType": "189", "messageId": "190", "endLine": 31, "endColumn": 29, "suggestions": "199"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 66, "column": 12, "nodeType": "189", "messageId": "190", "endLine": 66, "endColumn": 15, "suggestions": "200"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 78, "column": 12, "nodeType": "189", "messageId": "190", "endLine": 78, "endColumn": 15, "suggestions": "201"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 114, "column": 50, "nodeType": "189", "messageId": "190", "endLine": 114, "endColumn": 53, "suggestions": "202"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 9, "column": 44, "nodeType": "189", "messageId": "190", "endLine": 9, "endColumn": 47, "suggestions": "203"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 17, "column": 59, "nodeType": "189", "messageId": "190", "endLine": 17, "endColumn": 62, "suggestions": "204"}, {"ruleId": "180", "severity": 1, "message": "205", "line": 21, "column": 12, "nodeType": null, "messageId": "182", "endLine": 21, "endColumn": 17}, {"ruleId": "180", "severity": 1, "message": "205", "line": 42, "column": 12, "nodeType": null, "messageId": "182", "endLine": 42, "endColumn": 17}, {"ruleId": "180", "severity": 1, "message": "205", "line": 54, "column": 12, "nodeType": null, "messageId": "182", "endLine": 54, "endColumn": 17}, {"ruleId": "187", "severity": 2, "message": "188", "line": 4, "column": 13, "nodeType": "189", "messageId": "190", "endLine": 4, "endColumn": 16, "suggestions": "206"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 96, "column": 43, "nodeType": "189", "messageId": "190", "endLine": 96, "endColumn": 46, "suggestions": "207"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 96, "column": 49, "nodeType": "189", "messageId": "190", "endLine": 96, "endColumn": 52, "suggestions": "208"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 106, "column": 22, "nodeType": "189", "messageId": "190", "endLine": 106, "endColumn": 25, "suggestions": "209"}, {"ruleId": "180", "severity": 1, "message": "205", "line": 47, "column": 14, "nodeType": null, "messageId": "182", "endLine": 47, "endColumn": 19}, {"ruleId": "180", "severity": 1, "message": "205", "line": 75, "column": 14, "nodeType": null, "messageId": "182", "endLine": 75, "endColumn": 19}, {"ruleId": "180", "severity": 1, "message": "210", "line": 9, "column": 3, "nodeType": null, "messageId": "182", "endLine": 9, "endColumn": 14}, {"ruleId": "187", "severity": 2, "message": "188", "line": 2, "column": 34, "nodeType": "189", "messageId": "190", "endLine": 2, "endColumn": 37, "suggestions": "211"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 6, "column": 11, "nodeType": "189", "messageId": "190", "endLine": 6, "endColumn": 14, "suggestions": "212"}, {"ruleId": "213", "severity": 2, "message": "214", "line": 26, "column": 18, "nodeType": "195", "messageId": "215", "endLine": 26, "endColumn": 31, "suggestions": "216"}, {"ruleId": "213", "severity": 2, "message": "214", "line": 51, "column": 18, "nodeType": "195", "messageId": "215", "endLine": 51, "endColumn": 37, "suggestions": "217"}, {"ruleId": "213", "severity": 2, "message": "214", "line": 52, "column": 18, "nodeType": "195", "messageId": "215", "endLine": 52, "endColumn": 33, "suggestions": "218"}, "@typescript-eslint/no-unused-vars", "'request' is defined but never used.", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchInvoices'. Either include it or remove the dependency array.", "ArrayExpression", ["219"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["220", "221"], ["222", "223"], "prefer-const", "'endPage' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "224", "text": "225"}, "'Invoice' is defined but never used.", ["226", "227"], ["228", "229"], ["230", "231"], ["232", "233"], ["234", "235"], ["236", "237"], "'error' is defined but never used.", ["238", "239"], ["240", "241"], ["242", "243"], ["244", "245"], "'ApiResponse' is defined but never used.", ["246", "247"], ["248", "249"], "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "noEmptyInterfaceWithSuper", ["250"], ["251"], ["252"], {"desc": "253", "fix": "254"}, {"messageId": "255", "fix": "256", "desc": "257"}, {"messageId": "258", "fix": "259", "desc": "260"}, {"messageId": "255", "fix": "261", "desc": "257"}, {"messageId": "258", "fix": "262", "desc": "260"}, [538, 606], "const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);", {"messageId": "255", "fix": "263", "desc": "257"}, {"messageId": "258", "fix": "264", "desc": "260"}, {"messageId": "255", "fix": "265", "desc": "257"}, {"messageId": "258", "fix": "266", "desc": "260"}, {"messageId": "255", "fix": "267", "desc": "257"}, {"messageId": "258", "fix": "268", "desc": "260"}, {"messageId": "255", "fix": "269", "desc": "257"}, {"messageId": "258", "fix": "270", "desc": "260"}, {"messageId": "255", "fix": "271", "desc": "257"}, {"messageId": "258", "fix": "272", "desc": "260"}, {"messageId": "255", "fix": "273", "desc": "257"}, {"messageId": "258", "fix": "274", "desc": "260"}, {"messageId": "255", "fix": "275", "desc": "257"}, {"messageId": "258", "fix": "276", "desc": "260"}, {"messageId": "255", "fix": "277", "desc": "257"}, {"messageId": "258", "fix": "278", "desc": "260"}, {"messageId": "255", "fix": "279", "desc": "257"}, {"messageId": "258", "fix": "280", "desc": "260"}, {"messageId": "255", "fix": "281", "desc": "257"}, {"messageId": "258", "fix": "282", "desc": "260"}, {"messageId": "255", "fix": "283", "desc": "257"}, {"messageId": "258", "fix": "284", "desc": "260"}, {"messageId": "255", "fix": "285", "desc": "257"}, {"messageId": "258", "fix": "286", "desc": "260"}, {"messageId": "287", "fix": "288", "desc": "289"}, {"messageId": "287", "fix": "290", "desc": "289"}, {"messageId": "287", "fix": "291", "desc": "289"}, "Update the dependencies array to be: [currentPage, fetchInvoices]", {"range": "292", "text": "293"}, "suggestUnknown", {"range": "294", "text": "295"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "296", "text": "297"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "298", "text": "295"}, {"range": "299", "text": "297"}, {"range": "300", "text": "295"}, {"range": "301", "text": "297"}, {"range": "302", "text": "295"}, {"range": "303", "text": "297"}, {"range": "304", "text": "295"}, {"range": "305", "text": "297"}, {"range": "306", "text": "295"}, {"range": "307", "text": "297"}, {"range": "308", "text": "295"}, {"range": "309", "text": "297"}, {"range": "310", "text": "295"}, {"range": "311", "text": "297"}, {"range": "312", "text": "295"}, {"range": "313", "text": "297"}, {"range": "314", "text": "295"}, {"range": "315", "text": "297"}, {"range": "316", "text": "295"}, {"range": "317", "text": "297"}, {"range": "318", "text": "295"}, {"range": "319", "text": "297"}, {"range": "320", "text": "295"}, {"range": "321", "text": "297"}, {"range": "322", "text": "295"}, {"range": "323", "text": "297"}, "replaceEmptyInterfaceWithSuper", {"range": "324", "text": "325"}, "Replace empty interface with a type alias.", {"range": "326", "text": "327"}, {"range": "328", "text": "329"}, [1087, 1100], "[currentPage, fetchInvoices]", [1581, 1584], "unknown", [1581, 1584], "never", [7704, 7707], [7704, 7707], [939, 942], [939, 942], [1644, 1647], [1644, 1647], [1895, 1898], [1895, 1898], [2686, 2689], [2686, 2689], [306, 309], [306, 309], [536, 539], [536, 539], [99, 102], [99, 102], [2704, 2707], [2704, 2707], [2710, 2713], [2710, 2713], [2926, 2929], [2926, 2929], [55, 58], [55, 58], [121, 124], [121, 124], [404, 456], "type LoginResponse = ApiResponse<User>", [951, 1014], "type InvoiceListResponse = ApiResponse<Invoice[]>", [1022, 1079], "type InvoiceResponse = ApiResponse<Invoice>"]