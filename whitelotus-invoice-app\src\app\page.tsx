import { redirect } from 'next/navigation';
import { getCurrentUser } from '@/lib/auth';
import { ROUTES } from '@/lib/config';

export default async function Home() {
  // Check if user is authenticated
  const user = await getCurrentUser();

  if (user) {
    // Redirect authenticated users to dashboard
    redirect(ROUTES.DASHBOARD);
  } else {
    // Redirect unauthenticated users to login
    redirect(ROUTES.LOGIN);
  }
}
