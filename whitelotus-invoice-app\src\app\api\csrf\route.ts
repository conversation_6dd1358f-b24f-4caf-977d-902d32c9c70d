import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { generateCSRFToken, getSecurityHeaders } from '@/lib/security';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { status: 401, message: 'Unauthorized' },
        { status: 401, headers: getSecurityHeaders() }
      );
    }

    // Generate CSRF token
    const token = generateCSRFToken();

    return NextResponse.json({
      status: 200,
      message: 'CSRF token generated',
      token,
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}
