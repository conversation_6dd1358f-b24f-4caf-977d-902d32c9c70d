import { NextResponse } from 'next/server';
import { createLogoutCookie, getSecurityHeaders } from '@/lib/security';

export async function POST() {
  try {
    const response = NextResponse.json({
      status: 200,
      message: 'Logged out successfully',
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });

    // Clear the auth cookie
    const logoutCookie = createLogoutCookie();
    response.cookies.set(logoutCookie);

    return response;
  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}
