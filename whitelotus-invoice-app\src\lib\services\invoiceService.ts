import { apiClient, handleApiError, validateApiResponse } from '../api';
import { ROUTES } from '../config';
import {
  Invoice,
  InvoiceListResponse,
  InvoiceResponse,
  CreateInvoiceRequest,
  PaginationParams,
  ApiResponse,
} from '@/types';

export class InvoiceService {
  async getInvoices(params: PaginationParams = {}): Promise<InvoiceListResponse> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.skip !== undefined) {
        queryParams.append('skip', params.skip.toString());
      }
      
      if (params.limit !== undefined) {
        queryParams.append('limit', params.limit.toString());
      }

      const endpoint = `${ROUTES.API.INVOICES}?${queryParams.toString()}`;
      const response = await apiClient.get<InvoiceListResponse>(endpoint);
      
      return validateApiResponse<Invoice[]>(response);
    } catch (error) {
      throw handleApiError(error);
    }
  }

  async getInvoiceById(id: string): Promise<InvoiceResponse> {
    try {
      const endpoint = ROUTES.API.INVOICE_BY_ID(id);
      const response = await apiClient.get<InvoiceResponse>(endpoint);
      
      return validateApiResponse<Invoice>(response);
    } catch (error) {
      throw handleApiError(error);
    }
  }

  async createInvoice(invoiceData: CreateInvoiceRequest): Promise<InvoiceResponse> {
    try {
      const response = await apiClient.post<InvoiceResponse>(
        ROUTES.API.INVOICES,
        invoiceData
      );
      
      return validateApiResponse<Invoice>(response);
    } catch (error) {
      throw handleApiError(error);
    }
  }

  async downloadInvoice(filename: string): Promise<Blob> {
    try {
      const endpoint = ROUTES.API.DOWNLOAD_INVOICE(filename);
      const response = await fetch(`${apiClient['baseURL']}${endpoint}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to download invoice: ${response.statusText}`);
      }

      return await response.blob();
    } catch (error) {
      throw handleApiError(error);
    }
  }

  // Utility methods for formatting
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  }

  formatDate(dateString: string): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(dateString));
  }

  getStatusColor(status: Invoice['status']): string {
    switch (status) {
      case 'PAID':
        return 'text-green-600 bg-green-100';
      case 'PENDING':
        return 'text-yellow-600 bg-yellow-100';
      case 'OVERDUE':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  }

  isOverdue(dueDate: string): boolean {
    return new Date(dueDate) < new Date() && new Date(dueDate).toDateString() !== new Date().toDateString();
  }

  calculateDaysUntilDue(dueDate: string): number {
    const due = new Date(dueDate);
    const today = new Date();
    const diffTime = due.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
}

export const invoiceService = new InvoiceService();
