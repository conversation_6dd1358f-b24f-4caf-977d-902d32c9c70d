import { SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import { APP_CONFIG } from './config';
import { User } from '@/types';

const secret = new TextEncoder().encode(APP_CONFIG.JWT_SECRET);

export async function createToken(payload: any): Promise<string> {
  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(secret);
}

export async function verifyToken(token: string): Promise<any> {
  try {
    const { payload } = await jwtVerify(token, secret);
    return payload;
  } catch (error) {
    throw new Error('Invalid token');
  }
}

export async function getTokenFromCookies(): Promise<string | null> {
  const cookieStore = await cookies();
  return cookieStore.get(APP_CONFIG.COOKIE_NAME)?.value || null;
}

export async function getTokenFromRequest(request: NextRequest): Promise<string | null> {
  return request.cookies.get(APP_CONFIG.COOKIE_NAME)?.value || null;
}

export async function getCurrentUser(): Promise<User | null> {
  try {
    const token = await getTokenFromCookies();
    if (!token) return null;

    const payload = await verifyToken(token);
    return payload.user as User;
  } catch (error) {
    return null;
  }
}

export async function getCurrentUserFromRequest(request: NextRequest): Promise<User | null> {
  try {
    const token = await getTokenFromRequest(request);
    if (!token) return null;

    const payload = await verifyToken(token);
    return payload.user as User;
  } catch (error) {
    return null;
  }
}

export function createAuthCookie(token: string) {
  return {
    name: APP_CONFIG.COOKIE_NAME,
    value: token,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: APP_CONFIG.COOKIE_MAX_AGE,
    path: '/',
  };
}

export function createLogoutCookie() {
  return {
    name: APP_CONFIG.COOKIE_NAME,
    value: '',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 0,
    path: '/',
  };
}
