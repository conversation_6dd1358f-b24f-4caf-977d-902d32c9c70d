import { apiClient, handleApiError, validateApiResponse } from '../api';
import { ROUTES } from '../config';
import { LoginRequest, LoginResponse, User } from '@/types';

export class AuthService {
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>(
        ROUTES.API.LOGIN,
        credentials
      );
      
      return validateApiResponse<User>(response);
    } catch (error) {
      throw handleApiError(error);
    }
  }

  async logout(): Promise<void> {
    try {
      // Call logout endpoint if available, or just clear client-side state
      // Since the API doesn't have a logout endpoint, we'll handle it client-side
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
    } catch (error) {
      // Ignore errors during logout, just clear local state
      console.warn('Logout request failed:', error);
    }
  }

  async getCurrentUser(): Promise<User | null> {
    try {
      // This would typically call a /me endpoint
      // For now, we'll rely on the JWT token validation
      const response = await fetch('/api/auth/me', {
        credentials: 'include',
      });

      if (!response.ok) {
        return null;
      }

      const data = await response.json();
      return data.user;
    } catch (error) {
      return null;
    }
  }

  validateCredentials(email: string, password: string): string[] {
    const errors: string[] = [];

    if (!email) {
      errors.push('Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.push('Please enter a valid email address');
    }

    if (!password) {
      errors.push('Password is required');
    } else if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    return errors;
  }

  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      return true;
    }
  }
}

export const authService = new AuthService();
