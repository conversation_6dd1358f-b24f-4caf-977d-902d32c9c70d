import useSWR from 'swr';
import { invoiceService } from '@/lib/services/invoiceService';
import { Invoice, PaginationParams } from '@/types';

interface UseInvoicesOptions extends PaginationParams {
  refreshInterval?: number;
}

export function useInvoices(options: UseInvoicesOptions = {}) {
  const { skip = 0, limit = 5, refreshInterval = 0 } = options;
  
  const key = `/api/auth/invoice?skip=${skip}&limit=${limit}`;
  
  const fetcher = async () => {
    const response = await invoiceService.getInvoices({ skip, limit });
    if (response.status !== 200) {
      throw new Error(response.message || 'Failed to fetch invoices');
    }
    return response;
  };

  const { data, error, isLoading, mutate } = useSWR(key, fetcher, {
    refreshInterval,
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
    dedupingInterval: 5000, // 5 seconds
  });

  return {
    invoices: data?.data || [],
    pagination: (data as any)?.pagination,
    isLoading,
    error,
    mutate,
  };
}

export function useInvoice(id: string) {
  const key = id ? `/api/auth/invoice/${id}` : null;
  
  const fetcher = async () => {
    if (!id) return null;
    const response = await invoiceService.getInvoiceById(id);
    if (response.status !== 200) {
      throw new Error(response.message || 'Failed to fetch invoice');
    }
    return response.data;
  };

  const { data, error, isLoading, mutate } = useSWR(key, fetcher, {
    revalidateOnFocus: false,
    revalidateOnReconnect: true,
  });

  return {
    invoice: data,
    isLoading,
    error,
    mutate,
  };
}
