// API Response Types
export interface ApiResponse<T = any> {
  status: number;
  message: string;
  data?: T;
  error?: any;
}

// User Types
export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  is_active: boolean;
  token: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse extends ApiResponse<User> {}

// Invoice Types
export interface Invoice {
  id: string;
  invoice_number: string;
  client_name: string;
  amount: number;
  status: 'PENDING' | 'PAID' | 'OVERDUE';
  due_date: string;
  invoice_file_name: string;
  invoice_file_url: string;
  is_active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateInvoiceRequest {
  client_name: string;
  amount: number;
  status: 'PENDING' | 'PAID' | 'OVERDUE';
  due_date: string;
  invoice_file_name: string;
}

export interface InvoiceListResponse extends ApiResponse<Invoice[]> {}
export interface InvoiceResponse extends ApiResponse<Invoice> {}

// Pagination Types
export interface PaginationParams {
  skip?: number;
  limit?: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    skip: number;
    limit: number;
    total: number;
  };
}

// Form Types
export interface FormErrors {
  [key: string]: string | undefined;
}

// Auth Context Types
export interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  isAuthenticated: boolean;
}

// Error Types
export interface AppError {
  message: string;
  status?: number;
  code?: string;
}

// Component Props Types
export interface InvoiceCardProps {
  invoice: Invoice;
  onView?: (id: string) => void;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  totalItems: number;
}

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

// Security Types
export interface CSRFToken {
  token: string;
  expires: number;
}

// API Configuration
export interface ApiConfig {
  baseURL: string;
  timeout: number;
  retries: number;
}
