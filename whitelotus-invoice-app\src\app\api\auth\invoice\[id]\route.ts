import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { getSecurityHeaders, sanitizeApiResponse, checkRateLimit } from '@/lib/security';
import { mockInvoices } from '@/lib/mockData';

// GET /api/auth/invoice/[id] - Get invoice by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { status: 401, message: 'Unauthorized' },
        { status: 401, headers: getSecurityHeaders() }
      );
    }

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';
    if (!checkRateLimit(`invoice-detail:${clientIP}`, 60, 60000)) {
      return NextResponse.json(
        { status: 429, message: 'Too many requests' },
        { status: 429, headers: getSecurityHeaders() }
      );
    }

    const { id } = await params;

    // Validate ID format (basic UUID validation)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { status: 400, message: 'Invalid invoice ID format' },
        { status: 400, headers: getSecurityHeaders() }
      );
    }

    // Find invoice by ID
    const invoice = mockInvoices.find(inv => inv.id === id);

    if (!invoice) {
      return NextResponse.json(
        { status: 404, message: 'Invoice not found' },
        { status: 404, headers: getSecurityHeaders() }
      );
    }

    // Sanitize response data
    const sanitizedInvoice = sanitizeApiResponse(invoice);

    return NextResponse.json({
      status: 200,
      message: 'Invoice retrieved successfully',
      data: sanitizedInvoice,
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });
  } catch (error) {
    console.error('Get invoice error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}
