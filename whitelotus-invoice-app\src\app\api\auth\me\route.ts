import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { getSecurityHeaders } from '@/lib/security';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { status: 401, message: 'Unauthorized' },
        { status: 401, headers: getSecurityHeaders() }
      );
    }

    return NextResponse.json({
      status: 200,
      message: 'User retrieved successfully',
      user,
    }, {
      status: 200,
      headers: getSecurityHeaders(),
    });
  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json(
      { status: 500, message: 'Internal server error' },
      { status: 500, headers: getSecurityHeaders() }
    );
  }
}
