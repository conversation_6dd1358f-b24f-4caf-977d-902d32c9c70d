import { Suspense } from 'react';
import { notFound, redirect } from 'next/navigation';
import { getCurrentUser } from '@/lib/auth';
import { invoiceService } from '@/lib/services/invoiceService';
import { ROUTES } from '@/lib/config';
import { sanitizeApiResponse } from '@/lib/security';
import InvoiceDetailsContent from './InvoiceDetailsContent';
import LoadingSpinner from '@/components/LoadingSpinner';

// This page uses SSR with authentication check
export default async function InvoiceDetailsPage({
  params,
}: {
  params: { id: string };
}) {
  // Server-side authentication check
  const user = await getCurrentUser();
  
  if (!user) {
    redirect(ROUTES.LOGIN);
  }

  // Validate ID format
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(params.id)) {
    notFound();
  }

  let invoice;
  try {
    const response = await invoiceService.getInvoiceById(params.id);
    if (response.status === 200 && response.data) {
      // Sanitize the invoice data to prevent XSS
      invoice = sanitizeApiResponse(response.data);
    } else {
      notFound();
    }
  } catch (error) {
    console.error('Failed to fetch invoice:', error);
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <Suspense 
          fallback={
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          }
        >
          <InvoiceDetailsContent invoice={invoice} />
        </Suspense>
      </div>
    </div>
  );
}
